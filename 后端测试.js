const md5 = require('md5');

function generateWeChatPaySign(params, apiKey) {
  const sortedKeys = Object.keys(params).sort();
  let signString = sortedKeys.map(key => `${key}=${params[key]}`).join('&');
  signString += `&key=${apiKey}`;
  return md5(signString).toUpperCase();
}

const params = {
  appId: 'wx49c2324ccd5681b0',
  nonceStr: 'K3isPFXSNAeG2Qo7',
  package: 'prepay_id=wx13155049874079d2800b88a46fb7c00001',
  signType: 'MD5',
  packageValue: "Sign=WXPay",
  timeStamp: '1747122649'
};

const apiKey = '6ea19f1af5a476cd249adf884922c0b6';

const calculatedSign = generateWeChatPaySign(params, apiKey);

console.log('生成的:', calculatedSign);
console.log('Provided Sign:', 'C52A4C00972CE7E15093F321E2B2761C');
console.log('Match:', calculatedSign === 'C52A4C00972CE7E15093F321E2B2761C');