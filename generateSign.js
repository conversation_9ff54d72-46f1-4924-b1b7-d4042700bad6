const crypto = require('crypto'); // Node.js example, adapt for your environment

function generateSign(params, merchantKey) {
  const sortedKeys = Object.keys(params).sort();
  let stringA = '';
  for (let key of sortedKeys) {
    stringA += `${key}=${params[key]}&`;
  }
  stringA += `key=${merchantKey}`;
  return crypto.createHash('md5').update(stringA).digest('hex').toUpperCase();
}

const params = {
  appId: 'wx49c2324ccd5681b0',
  nonceStr: 'K3isPFXSNAeG2Qo7',
  package: 'wx13155049874079d2800b88a46fb7c00001',
  signType: 'MD5',
  timeStamp: '1747122649'
};
const merchantKey = '6ea19f1af5a476cd249adf884922c0b6'; // Replace with your actual key
const sign = generateSign(params, merchantKey);
console.log('Generated Sign:', sign);
console.log('Expected Sign:', 'C52A4C00972CE7E15093F321E2B2761C');